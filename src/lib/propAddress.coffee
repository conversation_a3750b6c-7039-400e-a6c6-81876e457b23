# 处理地址，一致化。
#Street Suffix Abbreviations
# convert all Commonly Used Street Suffix or Abbreviation to standard format
helpers_string = require './helpers_string'
helpers_number = require './helpers_number'
debugHelper = require './debug'
debug = debugHelper.getDebugger()
# DDF Address  format unt and addr.
cityHelper = require './cityHelper'
staticCityFields = require './staticCityFields'
streetCorrectHelper = require './streetCorrectHelper'

STREET_ABBR_CA_EN= require('./streetAbbr/CA_EN').STREET_ABBR
#https://gist.github.com/mirajshah/3760161b201755ffe73c

module.exports.DIRECTION_ABBR = DIRECTION_ABBR = {
  EAST:'E'
  WEST:'W'
  NORTH :'N'
  SOUTH : 'S'
  NW: 'NW'
  'N.W': 'NW'
  NORTHWEST:'NW'
  NE: 'NE'
  'N.E': 'NE'
  NORTHEAST:'NE'
  SW: 'SW'
  'S.W': 'SW'
  SOUTHWEST:'SW'
  SE: 'SE'
  'S.E': 'SE'
  SOUTHEAST:'SE'
  N: 'N'
  S: 'S'
  E: 'E'
  W: 'W'
}
STREET_NUMBER_WORD = {
  'FIRST':'1ST'
  'SECOND':'2ND'
  'THIRD':'3RD'
  'FOURTH':'4TH'
  'FIFTH':'5TH'
  'SIXTH':'6TH'
  'SEVENTH':'7TH'
  'EIGHTH':'8TH'
  'NINTH':'9TH'
  'TENTH':'10TH'
  'ELEVENTH':'11TH'
  'TWELFTH':'12TH'
  'THIRTEENTH':'13TH'
  'FOURTEENTH':'14TH'
  'FIFTEENTH':'15TH'
  'SIXTEENTH':'16TH'
  'SEVENTEENTH':'17TH'
  'EIGHTEENTH':'18TH'
  'NINETEENTH':'19TH'
  'TWENTIETH':'20TH'
  'TWENTY-FIRST':'21ST'
  'TWENTY-SECOND':'22ND'
  'TWENTY-THIRD':'23RD'
  'TWENTY-FOURTH':'24TH'
}

TORONTO_CITY_REG = /^TORONTO \w{3}$/i

# TORONTO_SUBCITY_MAP = {}
# for subcity in staticCityFields.subCityNameList
#   subcity = helpers_string.strCapitalize subcity,true
#   TORONTO_SUBCITY_MAP[subcity] = true


# UNIT_ABBR = {
#   Penthouse:'Ph'
#   Lt:'Lot'
# }
#TODO: number 处理？返回原始值还是空值？
module.exports.formatAddr = formatAddr = (addr)->
  return null if not addr?
  return addr if ('string' isnt typeof addr) or /^\d+$/.test(addr)
  addr = addr.toUpperCase()
  return formatStreetAbbrArray addr.split(/\s+/), 0

# case: deteache with common area
module.exports.hasMaintFee = hasMaintFee = (prop) ->
  return true if prop?.mfee
  return false

module.exports.isCondoOrTownHouse = isCondoOrTownHouse= (prop)->
  return false unless (ptype2 = prop.ptype2_en or prop.ptype2)
  ptype2 = [ptype2] unless Array.isArray ptype2
  for p in ptype2
    if /condo|apartment|townhouse/i.test p
      return true
  return false


# 判断是否为unit的辅助函数
isUnit = (part) ->
  part = part.trim()
  return false unless part
  isSimpleUnit = UNIT_REGEX.test(part)
  isComplexUnit = UNIT_PREFIX_REGEX.test(part)
  isBasementUnit = BASEMENT_REGEX.test(part)
  

# 从origAddr中提取纯净地址的函数
module.exports.extractAddressFromOrigAddr = extractAddressFromOrigAddr = (origAddr) ->
  return origAddr unless origAddr
  return origAddr unless ('string' is typeof origAddr)

  # 处理空字符串的情况
  if origAddr.trim() is ''
    return ''

  addr = origAddr.trim()

  # 如果包含逗号，需要判断是什么类型的格式
  if addr.includes(',')
    parts = addr.split(',')
    addressPart = parts[0].trim()

    if parts.length is 2
      # 两部分情况：判断哪一部分是unit
      firstPart = parts[0].trim()
      secondPart = parts[1].trim()

      if isUnit(firstPart) and /\d+.*[A-Za-z]/.test(secondPart)
        # 第一部分是unit，第二部分像地址，返回第二部分
        return secondPart
      else if isUnit(secondPart) and /\d+.*[A-Za-z]/.test(firstPart)
        # 第二部分是unit，第一部分像地址，返回第一部分
        return firstPart
      else
        # 都不是unit，返回完整地址
        return addr

    else if parts.length > 2
      # 超过两部分：只判断第一部分是否为unit
      if isUnit(addressPart)
        # 第一部分是unit，返回第二部分
        return parts[1].trim()
      else
        # 第一部分不是unit，返回第一部分
        return addressPart

    else
      # 只有一部分，返回第一部分
      return addressPart

  return addr

# import address format. convert all addr to capitalize and use abbr of street
formatStreetAbbrArray = (arry,fromIndex=0)->
  unless (Array.isArray arry)
    debug.debug arry
    throw new Error('Expect Array')
  unless arry.length
    throw new Error('Array length is 0')
  lastIndex = arry.length - 1
  arryLast = arry[lastIndex]?.replace(/[\.|\,]$/g,'').toUpperCase()

  #最后一个可能是数字，街道是倒数第二个。
  if /^\d+$/.test arryLast
    lastIndex--

  #最后一个可能是方向eg:W,E,SE
  if newDirection = DIRECTION_ABBR[arryLast]
    arry[lastIndex] = newDirection
    lastIndex--

  if lastIndex>=0
    arryLast = arry[lastIndex]?.replace(/[\.\,]$/,'').toUpperCase()

  if newAbbr = STREET_ABBR_CA_EN[arryLast]
    #8 The Esplanade should not convert to Esp
    arry[lastIndex] = newAbbr if arry[lastIndex-1]?.toUpperCase() isnt 'THE'
    lastIndex--

  #if last one and last second is same,remove one
  #地址里有重复街道的要去掉。两个重复的eg：AVE,AVE 去掉一个。
  # NOTE:重复街道可能一个为缩写,一个不为缩写 eg:3985 Grand Park Drive Dr N，Drive Dr需要去掉一个
  if lastIndex>=0 and newAbbr
    arryLast = arry[lastIndex]?.replace(/[\.\,]$/,'').toUpperCase()
    if newAbbr is STREET_ABBR_CA_EN[arryLast]
      arry.splice(lastIndex,1)
  #第二个字段是street name，可能需要转换
  if arry.length>2
    secondStr = arry[1]?.toUpperCase()
    if street = STREET_NUMBER_WORD[secondStr]
      arry[1]=street
      # debug.debug 'arry[1]',arry[1]

  #SE,SW,NE,NW 不用capitalize
  # debug.debug 'arry',arry
  if (lastStr = arry[arry.length - 1]?.toUpperCase()) in ['SW','SE','NE','NW']
    arry.length=arry.length-1
    str = helpers_string.strCapitalize arry.splice(fromIndex).join(' ')
    return str+' '+lastStr
  else
    return helpers_string.strCapitalize arry.splice(fromIndex).join(' ')

# 去掉#，unit, unit
#LT->Lot
#PENHOUSE -> Ph
#TODO: add test cases

###*
 * Format unt number, remove unnecessary characters
 * @param unt to format
 * @return unt formated unt
###
formatUnitAbbr = (unt)->
  return null unless unt
  return unt if 'number' is typeof unt
  # debug.debug 'formatUnitAbbr in unit',unt
  #TODO: remove U1->U,怎么处理？
  # Upper#2 -> Upper2
  if /UPPER/i.test unt.toUpperCase()
    unt = unt.toUpperCase().replace(/(UNIT|UNT|SUITE|^U\s|[#\,])\s*/gi,'')
    return helpers_string.strCapitalize unt
  unt = unt.toUpperCase().trim()
    .replace(/(UNIT|UNT|SUITE|^U\s|[#\,])\s*/gi,'')
    .replace(/^PENTHOUSE\s*/i,'Ph')
    .replace(/^LT/i,'Lot')
    .replace(/(\s)+/gi,' ')
    .replace(/^-/,'')
    .replace(/-$/,'')
    .trim()

  # debug.debug 'after replace',unt

  return helpers_string.strCapitalize unt

#TODO:used in ddf import format prop address, will be removed later.
module.exports.processPropUaddr = processPropUaddr = (prop)->
  return prop unless prop.addr
  ret = separateUnitFromAddr(prop.addr)
  prop.unt = ret.unt if ret.unt
  prop.addr = ret.addr if ret.addr
  prop

UNIT_PREFIX =
  '#': 1
  U: 1
  'U#': 1
  UNT: 1
  UNIT: 1
  'UNIT#': 1
  PH: 1
  UPH: 1
  LPH: 1
  SPH:1
  SLT:1
  SUITE: 1
  SUIT: 1

# addr='#lph4803 -50 Charles St E'时'#lph4803'为unit
UNIT_PREFIX_REGEX = /^#?(#|U|UNT|UNIT|PH|UPH|LPH|SPH|SLT|SUITE|SUIT)\s*(#|-)?\s*\d+/i
UNIT_REGEX =  /^[A-Za-z]?\d+[+&/-]?\d*[A-Za-z]?$/
BASEMENT_REGEX = /[^a-zA-Z0-9]+basement[^a-zA-Z0-9]*/i
STREET_NUMBER_REGEX = /^-?\d+-?\d*[A-Za-z]?-?$/
#处理情况：
# 横线分割：
# 1-123 abc street -> unt:1, addr:123 Abc St
#addr: 1-1-123 abc street, ret: unt:1-1, addr:123 Abc St
# PH-12-123 abc street ->  unt:PH-12, addr:123 Abc St
# LT开头：LT 20 main st, LT 2A 20 main street,LT-2A 20 main street
#3. 地址前面有#, u, unit.空格分割
#eg: #1 123 abc street,# 1 123 abc street, u 1 123 abc street, unit 1 123 abc street
#处理结果为：ret: unt:1, addr:123 Abc St
# PH 12 123 main st
# PH-12 123 main
# 123 main st. unit#123
# 123 main st. unit# 123
#Lot 199 212 Crestview Dr,Lot 11 Gord Vinson Ave
# 560 Uxbridge -pickering Line,85 Rue Alice-Parizeau？有unit，第二段要求有number，
#Pt Ne 3-48-27-W3？ properties_DDF_DDF22503548
#3876 Muskoka 118 Road Unit# Carling 10-W2
# 47479 Rge Rd 31 31
#3942 Squilax-Anglemont Hwy
## 10 2520 10 Ave Se
#3876 Muskoka 118 Road Unit# Carling 2 - W4
#'#215 1765 Leckie Road,'
#153070 Twp Rd 1-4 -> exp: 153070 Twp Rd 1, unt:4


CASES_MAPPING={
  a: 'remove comma at the end',
  b: 'return only format。 eg:HWY-3, Tusket, NS B0W 3M0, Canada'
  c: 'remove - at the end，eg:9640 92 Avenue Unit# 8-'
  d: 'split by last -,first part is unt, second is addr,eg:PH-12-123 abc street'
  'd1':'第一部分与第二部分最后相同都是unit, eg: 309-220 KENYON STREET W UNIT#309, unit:309'
  'd2':'第一部分与第二部分开头相同都是unit, eg: #904-904 155 YORKVILLE AVE, unit:904'
  'd3':'第一部分与第二部分首位连起来构成unit, eg:#1-4 13635 34 ST NW, unit:#1-4'
  e: 'remove last # and continue,eg:123 main st. unit#7260'
  f: '3part or is Lot: eg:123 main st; 235 Paradise Lane E'
  g: 'is Lot,eg:LT 20 main st'
  h: 'is Lot,has unt, eg:LT 2A 20 main street'
  i: 'lt has unt, LT-2A 20 main street'
  j: 'unit in front with space,eg:ph 123 abc street'
  k: 'unit in front with no space,eg:unt1209 11811 LAKE FRASER DR SE'
  l: '倒数第二位是unit prefix,eg: 380 PELISSIER Unit# 210'
  m: 'unit在最后，数字和unit没有空格,380 PELISSIER Unit#210'
  n: '第二段是字符串，没有unit,eg:123 abc street ave'
  o: '街道名是数字，没有unt,eg: 123 19 STREET.'
  p: '第一位是unt,eg: 10 2520 10 ave se'
  q: '最后一位是penthouse,30 Indianola Crescent #penthouse'
  r: '246 Scott Ave.,lower Unt'
  s: 'Lot开头。有 unit,eg:Lot 235 Paradise Lane E Fr#866'
  t: 'Lot开头。no unit Lot 235 Paradise Lane E'
  u: 'basement following by special character, eg: 9 Nobert (basement) Rd, 8 Orleans -basement Dr'
  z: 'no match'
  org: 'use orig unit'
}
###*
 * Seperate unt from address
 * @param {string} orgAddr, address to format
 * @param {string} orgUnit, unit
 * @return {unt,addr} formated address
###
module.exports.separateUnitFromAddr = separateUnitFromAddr = (orgAddr,orgUnit)->
  unt = null
  cases=[]
  if 'string' isnt typeof orgAddr
    debug.warn 'orgAddr is not a string:', orgAddr
    return {unt:null, addr:orgAddr?.toString() or null, showAddr:orgAddr}
  addr = orgAddr.replace(/\s+/g, ' ').trim()
  addr = addr.replace(/[\[\]]/g,' ')

  buildReturn = (unt, addrArray,cases)->
    startReg = /^[-|#|&|+]/
    endReg = /[-|#|&|+](LOT)?$/i
    # 去掉符号开头
    if startReg.test addrArray[0]
      addrArray[0] = addrArray[0].replace(startReg,'')
    if endReg.test addrArray[addrArray.length-1]
      addrArray[addrArray.length-1] = addrArray[addrArray.length-1].replace(endReg,'')
    # 去除unit
    addrArray = addrArray.filter (item)->
      return not UNIT_PREFIX[item.toUpperCase()]

    # showAddr保持原始格式，不进行街道缩写等格式化
    showAddr = addrArray.join(' ').trim().replace(startReg,'')
    addr = formatStreetAbbrArray(addrArray,0).replace(startReg,'')
    return {
      unt,
      showAddr,
      addr:addr.trim(),
      cases
    }

  # exists original unit
  if orgUnit
    orgUnit += ''
    orgUnit = orgUnit.replace(/,/g, ' ').trim()
    formatUnit = formatUnitAbbr orgUnit
    if /,/.test(orgAddr)
      orgAddr = orgAddr.replace(/,/g, ' ').trim()
      cases.push 'a'
    orgAddrArr = orgAddr.split(/\s+/)
    if (orgAddr.toUpperCase().includes orgUnit.toUpperCase()) or (orgAddr.toUpperCase().includes formatUnit.toUpperCase())
      # addr中包含unit信息,先解析判断解析的unt是否一致
      ret = separateUnitFromAddr orgAddr
      ret.cases = ret.cases.concat cases
      # orig unit与解析出来unit一致，直接返回
      if (ret.unt is formatUnit) or (ret.unt?.toUpperCase() is orgUnit.toUpperCase())
        return ret
      # orig unit提供出错, eg:4 Heritage Way Unit 204, orgUnit:4, actual unit:204
      # case 'r','l' addr最后面可以确认的unit信息
      else if (ret.cases.includes('r')) or (ret.cases.includes('l'))
        debug.warn "orgUnit is wrong, origAddr:#{orgAddr}, orgUnit:#{orgUnit}, parsed result:",ret
        return ret
      # case 'f'代表street仅由3部分组成,避免unit与street_num一致
      else if (not ret.cases.includes('f'))
        if (/^LOT/i.test ret.unt) and (formatUnit is ret.unt.replace(/^LOT/i,'').trim())
          return ret
        unitArr = orgUnit.split(/\s+/)
        charReg = /^#?(#|UNIT|UNT|U|PH|UPH|LPH|SPH|SLT|SUITE|SUIT)[-|#|&|+]?/i
        # 记录已经从addr中去掉的unt,防止出现st_num和unt一样的情况
        untMap = {}
        orgAddrArr = orgAddrArr.filter (item)->
          if (charReg.test item) and (item.length > 1)
            item = item.replace(charReg,'')
          for val in unitArr
            if (charReg.test val) and (val.length > 1)
              val = val.replace(charReg,'')
            if (not untMap[val]) and (val?.toUpperCase() is item?.toUpperCase())
              untMap[val] = true # 已经从address中去掉的unt,不再去掉第二次
              return false
          return true
      # case 'u'代表存在'basement',需要应对'#Basement - 31 Avanti Crescent',unit为'Basement'的情况
      else if (ret.cases.includes('u')) and ('BASEMENT' is formatUnit.toUpperCase())
        ret.unt = formatUnit
        return ret
      ret.cases.push 'org'
      return buildReturn formatUnit,orgAddrArr,ret.cases
    else
      # origAddr中不包含unit信息,保留原addr
      cases.push 'org'
      return buildReturn formatUnit,orgAddrArr,cases

  #.toUpperCase()
  ###*
  * cases a: remove comma at the end,eg:'Lot 14 Canoe Cove Rd.,'
  ###
  if /,/.test(addr)
    addr = addr.replace(/,/g, ' ').trim()
    cases.push 'a'
    # debug.debug 'case a ',orgAddr
  #debug.debug "\n orgAddr： #{addr}"
  ###*
  * cases b: 包含字母-数字,only format。 eg:HWY-3, Tusket, NS B0W 3M0, Canada
  ###
    
  if (/^[^0-9]+-\d+$/.test addr) or (/[0-9]+\s[A-Za-z]+\s*-\s*\d+$/.test addr) #'7 HWY - 3', HWY-3
    cases.push 'b'
    debug.debug 'case b ',orgAddr
    return buildReturn(null,[addr],cases)

  ###
    * cases u: remove basement in addr
    * eg: 8 Orleans -basement Dr
    *     2553 Homelands *basement Dr
    *     4 Hisey [basement] Cres
    *     9 Nobert (basement) Rd
    *     50 Pinery(basement) Lane
  ###
  if (BASEMENT_REGEX.test addr)
    cases.push 'u'
    addr = addr.replace BASEMENT_REGEX, ' '

  ###*
  * cases c: remove - at the end，eg:9640 92 Avenue Unit# 8-
  ###
  indexOfLastDash = addr.lastIndexOf('-')
  if indexOfLastDash is addr.length-1
    # debug.debug 'case c ',orgAddr
    cases.push 'c'
    addr = addr.substr(0,addr.length-1).trim()
    indexOfLastDash = addr.lastIndexOf('-')
  
  #-前面的第一个
  firtPart = addr.substr(0,indexOfLastDash).trim().toUpperCase()

  # split by -, avoid: PH-12 123 main
  # addr:1-123  abc street ->  unt:1, addr:123 Abc St
  # PH-12-123 abc street ->  unt:PH-12, addr:123 Abc St
  # unt length <6.,addr should has number
  #有多于一个 - 的，用最后一个 - 区分
  
  formatedFirstElem = formatUnitAbbr(firtPart)
  #LT是LOT,不是unit，
  firstElementIsNotPrefix = (not UNIT_PREFIX[firtPart]) and (firtPart isnt 'LT')
  secondElement = addr.substr(indexOfLastDash+1).trim()
  secondElementHasNumber = /\d/.test(secondElement)

  ###*
  * cases d: split by -, first part is less then 7 and not a unit prefix
  * and second part has st number
  * eg: PH-12-123 abc street, unt:PH-12, addr:123 abc street
  ###
  if (indexOfLastDash > 0) and (formatedFirstElem.length < 7) \
    and firstElementIsNotPrefix
      # debug.debug 'case d ',orgAddr
      # eg:309-220 KENYON STREET W UNIT#309
      if idx = secondElement.lastIndexOf('#')
        firstStr = secondElement.substr(0,idx).replace(/\s+-\s+/g,' ').trim()
        secondStr = secondElement.substr(idx+1).trim()
        if formatUnitAbbr(secondStr) is formatedFirstElem
          cases.push 'd1'
          return buildReturn(formatedFirstElem,firstStr.split(/\s+/),cases)
      # eg: #8 -15 28TH ST N
      if (/^\d+\s\d+/.test secondElement) and (UNIT_REGEX.test formatedFirstElem)
        secondArr = secondElement.split(/\s+/)
        # eg: #2-204 4245 139 AV NW
        if (secondArr.length > 3) and (UNIT_REGEX.test secondArr[0]) and \
        (STREET_NUMBER_REGEX.test secondArr[1]) and (not STREET_ABBR_CA_EN[secondArr[2].toUpperCase()])
          # eg: #904-904 155 YORKVILLE AVE  unit:9
          # eg: #1-1 13635 34 ST NW unit:1-1
          if (formatedFirstElem is secondArr[0]) and (formatedFirstElem.length > 1)
            cases.push 'd2'
            return buildReturn(formatedFirstElem,secondArr.slice(1),cases)
          else
          # eg: #1-4 13635 34 ST NW
            cases.push 'd3'
            unt = formatedFirstElem + '-' + secondArr.shift()
            return buildReturn(unt,secondArr,cases)
      cases.push 'd'
      return buildReturn(formatedFirstElem,secondElement.split(/\s+/),cases)

  ###*
    * cases e: last index of # greater then 5, remove #
    * eg: 123 main st. unit#7260
    * #320 Croft Unit# 1-3
  ###
  
  if ((indexSharp = addr.lastIndexOf('#')) > 5)
    # debug.debug 'case e',orgAddr
    cases.push 'e'
    
    # case: 3460 South Millway|Unit #23
    firstStr = addr.substr(0,indexSharp)
    if firstStr.includes('|') and /(U|UNT|UNIT|PH|UPH|LPH|SPH|SLT|SUITE|SUIT)/.test(firstStr)
      firstStr = firstStr.replace(/\|/g,' ')
      
    firstStr = firstStr.replace(/\s+[-|\/|&|+]\s+/g,' ').trim()
    secondStr = addr.substr(indexSharp+1).replace(/\s+[-|\/|&|+]\s+/g,' ').trim()
    # '#'后可能是street,不对addr做额外处理
    # #ROOM #3 -3 - 91 WESTMOUNT RD N
    firstStrLastElement = firstStr.toUpperCase().split(/\s+/).pop()
    if (secondStr.split(/\s+/).length > 3) and (not UNIT_PREFIX[firstStrLastElement])
      arr = addr.replace(/\s+-\s+/g,' ').trim().split(/\s+/)
    else
    # '#'前是street
      unt = addr.substr(indexSharp+1).trim()
      addr = firstStr
      arr = addr.split(/\s+/)
      arr.push unt
    # debug.debug 'case e arr',arr, 'unt',unt
  else
    #153070 Twp Rd 1-4 to be unt: 4,addr: 153070 Twp Rd 1, return if has dash.
    if (indexOfLastDash is (addr.length - 2)) and \
    (addr.length > 3) and (/^\d/.test firtPart)
      cases.push 'ca'
      return buildReturn(secondElement,[firtPart],cases)

    arr = addr.replace(/\s+-\s+/g,' ').trim().split(/\s+/)
  # process 5289 HWY 7
  #debug.debug arr
  # debug.debug 'arr',arr
  arrLen = arr.length

  ###*
    * cases f: 长度为3，只有地址
    * eg: 123 main st,
  ###
  if arrLen <=3
    # debug.debug 'case f',orgAddr
    cases.push 'f'
    return buildReturn(null,arr,cases)
  # eg: 712 Rossland Rd E,只有地址
  if (arrLen is 4) and (DIRECTION_ABBR[arr[3]])
    cases.push 'f'
    return buildReturn(null,arr,cases)
  firtWord = arr[0]?.toUpperCase()
  if  (firtWord is 'LOT') #认为lot没有unit，但是可能有。
    # debug.debug 'case s',orgAddr
    cases.push 's'
    ###*
      * cases s: Lot开头。有 unit
      * Lot 235 Paradise Lane E Fr#866
    ###
    if arrLen >3 and helpers_number.isNumber(arr[arrLen-1])
      cases.push 's'
      return buildReturn(
        formatUnitAbbr(arr[arrLen - 1]),arr.splice(0,arrLen-1),cases
      )
    else
      ###*
      * cases t: Lot开头。no unit
      * eg: Lot 235 Paradise Lane E
      ###
      cases.push 't'
      return buildReturn(null,arr,cases)
  if arr[0] is 'LT'
    ###*
      * cases g: LT is first, no unit
      * eg: LT 20 main st
    ###
    if isNaN(arr[2])
      cases.push 'g'
      # debug.debug 'case g',orgAddr
      return buildReturn(null,arr,cases)
    else
      ###*
      * cases h: LT is first, has unit_num
      * eg: LT 2A 20 main street
      * unt: Lot2a, addr:20 main St
      ###
      cases.push 'h'
      # debug.debug 'case h',orgAddr
      return buildReturn(formatUnitAbbr(arr[0]+' '+arr[1]),arr.splice(2),cases)

  ###*
    * cases i: LT- in beginning, is unit_num
    * eg: LT-2A 20 main street
    * unt: Lot-2a, addr:20 main St
  ###
  if /^LT-/.test(firtWord)
    cases.push 'i'
    # debug.debug 'case i',orgAddr
    return buildReturn(formatUnitAbbr(arr[0]),arr.splice(1),cases)

  ###*
    * cases j: unit in front with space
    * eg: ph 123 abc street, u 1 123 abc street, unit 1 123 abc street
  ###
  if UNIT_PREFIX[firtWord]
    cases.push 'j'
    # debug.debug 'case j',orgAddr
    return buildReturn(formatUnitAbbr(arr[0]+' '+arr[1]),arr.splice(2),cases)

  ###*
    * cases k: unit in front with no space
    * eg: unt1209 11811 LAKE FRASER DR SE, #1 123 abc street,
  ###
  if UNIT_PREFIX_REGEX.test(firtWord)
    cases.push 'k'
    # debug.debug 'case k',orgAddr,arr[0],arr
    return buildReturn(formatUnitAbbr(arr[0]),arr.splice(1),cases)

  ###*
    * cases r
    * eg: 246 Scott Ave.,lower Unt
  ###
  if (UNIT_PREFIX[arr[arrLen - 1]?.toUpperCase()]) \
  and (arr[arrLen - 2]?.toUpperCase() in ['UPPER','LOWER','PENTHOUSE'])
    cases.push 'r'
    # debug.debug 'case r',orgAddr
    return buildReturn(formatUnitAbbr(arr[arrLen - 2]+' '+arr[arrLen - 1])\
    ,arr.splice(0,arrLen-2),cases)

  ###*
    * cases l: 倒数第二位是unit prefix,或者，#
    * eg:# 380 PELISSIER Unit# 210
    * #150 Prospect Street Unit  Penthouse
  ###
  if UNIT_PREFIX[arr[arrLen - 2].toUpperCase()]
    cases.push 'l'
    # debug.debug 'case l',orgAddr
    return buildReturn(formatUnitAbbr(arr[arrLen - 2]+' '+ arr[arrLen - 1]),\
    arr.splice(0,arrLen-2),cases)

  ###*
    * cases q: 最后一位是PENTHOUSE
    * eg:# 30 Indianola Crescent #penthouse
  ###
  if arr[arrLen - 1].toUpperCase() is 'PENTHOUSE'
    cases.push 'q'
    # debug.debug 'case q',orgAddr
    return buildReturn(formatUnitAbbr(arr[arrLen - 1]),\
    arr.splice(0,arrLen-1),cases)

  
  #47479 Rge Rd 31 302 to be unt 302
  # 47479 Rge Rd 31 302
  #23354 Township Rd 523, skip parse
  exceptSt = ['Township Road','Township Rd']
  excepRegex = new RegExp("(#{exceptSt.join('|')})", 'i')
  caseMa = (arrLen > 3) \
    and (/^\d+$/.test arr[arrLen - 1]) \
    and (/^\d+$/.test arr[0])

  if UNIT_PREFIX_REGEX.test(arr[arrLen - 1]) \
  or (caseMa and (not (excepRegex.test addr)))
    ###*
      * cases m: unit在最后，数字和unit没有空格，unitPrefix+数字
      * eg: 380 PELISSIER Unit#210
      * 47479 Rge Rd 31 302 最后两个都是数字。长度5以上
    ###
    cases.push 'm'
    # debug.debug 'case m',orgAddr
    return buildReturn(formatUnitAbbr(arr[arrLen - 1]),\
    arr.splice(0,arrLen-1),cases)

  ###*
    * cases n: 第二段是字符串，没有unit
    * eg: 123 abc street ave .
  ###
  # 第二段有可能是street,需要过滤掉
  if isNaN(arr[1]) and (arr[1] not in ['+','&','-']) and (not STREET_ABBR_CA_EN[arr[1].toUpperCase()])
    # 210 G4 4653 Blackcomb Way,unit为210 G4
    # 3 At 399 Spring Garden Ave,unit为3
    # 407 C&h 187 Kananaskis Way
    # A 1/8 800 Bighorn Blvd
    # 202/201 A 366 Clubhouse Dr
    if (UNIT_REGEX.test arr[0]) and (STREET_NUMBER_REGEX.test arr[2])
      cases.push 'p'
      return buildReturn("#{arr[0]} #{arr[1]}",arr.slice(2),cases)
    else if (UNIT_REGEX.test arr[0]) and (STREET_NUMBER_REGEX.test arr[1])
      # 301 2842-2856 Gottingen St , unit=301,st_num=2842-2856
      cases.push 'p'
      return buildReturn(arr[0],arr.slice(1),cases)
    else
      cases.push 'n'
      return buildReturn(null,arr,cases)
  ###*
    * cases o: ，街道名是数字，没有unt, length is 3 or 4.
    * eg: 123 19 STREET.
    * eg: 11311 25 REGIONAL RD
    * eg: 2108 10 St NW
    * 这种不算是这个 case: 304 1233 AVENUE RD, 这是 unt: 304, st_num: 1233, st: AVENUE RD
    ###
  arr2 = arr[2]?.replace(/[\.\,]$/,'').toUpperCase()
  arr3 = arr[3]?.replace(/[\.\,]$/,'').toUpperCase()
  if (arr2 and STREET_ABBR_CA_EN[arr2] and ((not arr3) or DIRECTION_ABBR[arr3])) or \
    (arr3 and STREET_ABBR_CA_EN[arr3] and (arr2 is 'REGIONAL'))
      cases.push 'o'
      # debug.debug 'case o',orgAddr
      return buildReturn(null,arr,cases)

  ###*
    * cases p: 第一位是unt,后半段第一位有数字
    * eg: 10 2520 10 ave se，1 123 main st
    * eg: 138&139 970 Burrard St
    * eg: 702/704 4050 Whistler Way
    * eg: 1222/1224 - 1200 Riverside Way
    * eg: 411 & 412 4885 Kingsway
    * eg: 309 & 307-156 Government St
    * eg: 207 & 207b - 245 Columbia St
    * eg: 204|302 - Coverdale Rd
  ###

  tmpAddr = arr.join(' ')
  if /^\d+\s+&\s+\d+/.test tmpAddr
    tmpAddr = tmpAddr.replace(/\s+&\s+/,'&')
  tmpAddr = tmpAddr.replace(/\s+[-/#]\s+/g,' ')
  tmpAddr = tmpAddr.replace(/[|]/g,' ')
  arr = tmpAddr.split(/\s+/)
  first = formatUnitAbbr(arr[0])
  second = arr.slice(1)

  if ((UNIT_REGEX.test first) or (/^[A-Za-z]$/.test first) or (first.length < 7)) and (second.length) and STREET_NUMBER_REGEX.test(second[0])
    cases.push 'p'
    debug.debug 'case p',orgAddr
    return buildReturn(first,second,cases)

  ###*
    * cases q: 不match 任意情况，format并返回
  ###
  cases.push 'z'
  debug.debug 'case z',orgAddr
  return buildReturn(null,arr,cases)

# filterCAZip
module.exports.filterZip = filterZip = (zip)->
  return null if not zip
  return zip if not ('string' is typeof zip)
  zip = zip.toUpperCase()
  if zip?.length >= 6
    zipCA = zip.replace(/[^A-Z0-9]/g,'').substr(0,6)
    if /^[A-Z]\d[A-Z]/.test(zipCA)
      return zipCA
  zip

#Wrong -> correct
module.exports.cmtyWrongToCorrect = cmtyWrongToCorrect = {
  'Saltspring Island':'Salt Spring Island'
  'Waterfront Communities C1':'Waterfront Communities C01'
  'Waterfront Communities C8':'Waterfront Communities C08'
  'Cabbagetown-South St. Jamestown':'Cabbagetown-South St. James Town'
  'West Humber-Claireville':'West Humber-Clairville'
  'Cenral Erin Mills':'Central Erin Mills'
  'Westminister-Branson':'Westminster-Branson'
  'Windfi elds':'Windfields'
  'Trinity Bellwoods':'Trinity-Bellwoods'
  'Thistletown-Beaumond Heights':'Thistletown-Beaumonde Heights'
  'Bramton West':'Brampton West'
  'Markland Woods':'Markland Wood',
  # 'Tm Timberlea':'Timberlea',
  'Wm Westmount':'Westmount'
  # 'Wt West Oak Trails','West Oak Trails'
  'Fd Ford':'Ford',
  'Cv Clearview':'Clearview',
  'Mississauga Valley':'Mississauga Valleys',
  'Twmc-Mimico':'Mimico',
  'Twic-Islington-City Centre West':'Islington-City Centre West'
  # 'Or61-Hawkestone','Hawkestone'
  # 'Se54-Washago','Washago'
  # 'Ra40-Rural Ramara', 'Rural Ramara'
  # 'ZZ-OL01-ORILLIA':'Orillia'
  }

module.exports.correctCmty = correctCmty = (cmty)->
  return unless cmty
  return null if /N\/A/i.test cmty
  cmty = helpers_string.strCapitalize cmty,true
  #Emerald Meadows, Trailwest
  cmty = cmty.split(',')[0].trim()
  #BOBCAYGEON (TOWN)
  cmty = cmty.split('(')[0].trim()

  #1038 - Wi Willmot -> Willmot
  cmty = cmty.replace(/^(\d)+\s*-\s*/,'')

  #remove space within -
  cmty = cmty.replace(/(\s)*-(\s)*/,'-')
  cmty = cmty.replace(/(\s)*\/(\s)*/,'/')

  #remove non char in the end,eg:EMPIRE SOUTH.
  cmty = cmty.replace(/(\.|\/)$/,'')

  #ZZ-OL01-ORILLIA
  exceptRegex = /^(ST|MT|LA|LE|SU|PE)/i
  if (/^([A-Za-z]){2}-([A-Za-z0-9]{4})-/.test(cmty)) and \
    (not (exceptRegex.test cmty))
      cmty = cmty.split('-').splice(2).join(' ')
  #'Cl Clarke'
  else if /^[A-Za-z]{2}\s/.test(cmty)
    arr = cmty.split(' ')
    len = arr.length
    if (len is 2) and (new RegExp("^#{arr[0]}", 'i').test arr[1])
      cmty = arr[1]
    #'Wt West Oak Trails', any first letter.
    else
      first = arr[0]
      arr.shift()
      allFirstLetter = arr.reduce (accumulator, currentValue)->
        return accumulator + currentValue[0]
      , ''
      if new RegExp("^#{first[0]}[A-Za-z]*#{first[1]}", 'i').test allFirstLetter
        cmty = arr.join(' ')

  else if /^[A-Za-z]{2}(\d){2}\s/.test(cmty) or \
    /^(\d){2}\s/.test(cmty) #'Tm01 Timberlea'
      cmty = cmty.substring(cmty.indexOf(' ')+1)
  else if /^[A-Za-z]{2}(\d){2}-/.test(cmty) #'Ra40-Rural Ramara'
    cmty = cmty.substring(cmty.indexOf('-')+1)
  #Tcw1-Waterfront Communities C1
  else if /^T[A-Za-z]{3}-/.test(cmty) or /^T[A-Za-z]{2}[0-9]{1}-/.test(cmty)
    cmty = cmty.substring(cmty.indexOf('-')+1)
  if correct = cmtyWrongToCorrect[cmty]
    cmty = correct
  cmty

# st: 'Peace River',
# st_dir: 'N',
# st_num: '160',
# st_sfx: 'Ave',
buildAddrFromSt=(obj)->
  return null if not obj
  # NOTE: st可能为数字, 不能判断st.length > 1
  # eg: propId: CLGA2184111, 730 2 Ave SW
  st = obj.st?.toString()
  if st?.length > 0
    # 处理st_dirpfx字段
    # 如果st_dirpfx不等于st_dir，将st_dirpfx加在st的最前面
    if obj.st_dirpfx and obj.st_dirpfx isnt obj.st_dir
      st = "#{obj.st_dirpfx} #{st}"
    parts = [obj.st_num, st, obj.st_sfx, obj.st_dir].filter((part) -> part?)
    return parts.join(' ')
  null

# @return
# type qaddr = string
# eg. 30530 Cardinal Avenue Ave, Abbotsford, BC N0N0N0, CA
#                5097 Alyssa Dr, Beamsville, ON L0R1B2, CA
module.exports.getFullAddress = getFullAddress=(obj,appendZip=true,appendCmty=false) ->
  if (not obj.city) or (not obj.prov)
    return null
  addr = obj.addr or buildAddrFromSt obj
  prov = cityHelper.getProvAbbrName(obj.prov, obj.cnty)
  zip = if obj?.zip and appendZip then obj.zip else ''
  prov ="#{prov} #{zip}" if zip
  qaddr = ''
  qaddr = "#{addr}, " if addr
  cmty = if obj?.cmty and appendCmty then obj.cmty else ''
  qaddr += "#{cmty}, " if cmty # 加入cmty查询准确率更高
  qaddr += "#{obj.city}, #{prov}, #{obj.cnty or 'CA'}"
  qaddr

###*
 * 从地址字符串中解析出街道后缀(st_sfx)和方向(st_dir)
 * @param {string} addr - 完整地址字符串
 * @returns {Object} 包含解析结果的对象
 * @property {string} st_sfx - 街道后缀,如 Ave, St 等
 * @property {string} st_dir - 方向,如 N, S, E, W 等
 * @property {Array<string>} addrArr - 去除后缀和方向后的地址数组
###
module.exports.decodeSfxAndDirFromAddr = decodeSfxAndDirFromAddr = (addr)->
  # 初始化返回值
  st_sfx = null
  st_dir = null
  return {st_sfx, st_dir, addrArr:[]} unless addr
  # 将地址按空格分割成数组
  addrArr = addr.split(' ')
  
  # 获取最后一个元素
  lastElement = addrArr[addrArr.length-1]
  
  # 处理最后一个元素为空的情况
  if not lastElement
    lastElement = ''
    debug.error 'Error: decodeSfxAndDirFromAddr', 'lastElement is null', addr
    return {st_sfx, st_dir, addrArr:[]}
  
  # 检查最后一个元素是否为街道后缀
  if STREET_ABBR_CA_EN[lastElement.toUpperCase()]
    st_sfx = lastElement
    addrArr.pop()
    
    # 检查倒数第二个元素是否为方向
    lastElement = addrArr[addrArr.length-1]
    if lastElement and DIRECTION_ABBR[lastElement.toUpperCase()]
      st_dir = lastElement
      addrArr.pop()
      
  # 检查最后一个元素是否为方向
  else if DIRECTION_ABBR[lastElement.toUpperCase()]
    st_dir = lastElement
    addrArr.pop()
    
    # 检查倒数第二个元素是否为街道后缀
    lastElement = addrArr[addrArr.length-1]
    if lastElement and STREET_ABBR_CA_EN[lastElement.toUpperCase()]
      st_sfx = lastElement
      addrArr.pop()

  return {st_sfx, st_dir, addrArr}

# 解析地址中的st_sfx与st_dir
module.exports.parseSfxAndDir = parseSfxAndDir = (obj) ->
  if not obj.addr
    # NOTE:RM房源,googleGeocode会出现没有addr的情况
    debug.warn 'prop has no addr,prop:',obj
    return
  {st_sfx,st_dir,addrArr} = decodeSfxAndDirFromAddr obj.addr
  obj.st_sfx ?= helpers_string.strCapitalize st_sfx if st_sfx
  obj.st_dir ?= st_dir if st_dir
  obj.dir ?= st_dir if st_dir # 以前使用的dir字段
  return addrArr

###
# 判断是否需要执行separateUnitFromAddr函数
# 1.判断condo/townhouse or has maint fee，if是，执行separateUnitFromAddr
# 2.不是condo，判断出租，是出租执行separateUnitFromAddr, 因为有可能分租
# 3.不是condo，不是出租，不执行separateUnitFromAddr
# 4.源数据含有unt字段时执行separateUnitFromAddr
# @params {object} {
  ptype2: array  - required,需要判断是否为condo/townhouse
  saletp: array  - required,需要判断是否为出租房源
  mfee: string  - not required
  unt: string  - not required
}
###
module.exports.checkIsRunFuncSeparateUnitFromAddr = checkIsRunFuncSeparateUnitFromAddr = (prop)->
  if hasMaintFee(prop) or isCondoOrTownHouse(prop)
    return true
  else if /lease|rent/i.test prop.saletp
    return true
  else if prop.unt
    return true
  else
    return false

module.exports.checkPropCity = checkPropCity = (prop)->
  {city,prov,cmty,src} = prop
  if not (city and prov)
    return city
  result = city.toUpperCase().replace(/^\,/g,'').trim()
  # Toronto需要特殊处理,cases:Toronto C07
  if TORONTO_CITY_REG.test result
    result = result.split(' ')[0]
  # city中含有数字与特殊字符,需要warnning log
  # 存在Mcmurrich/Monteith，St. Louis，Plympton-Wyoming，St. John's的城市名,暂时不判断/'-.字符
  if /[\[\]()+=&|><!{}^\"~*?:0-9]/.test result
    debug.warn "Not common city:#{city}"
    # 去掉DDF房源中city中含有cmty的信息(暂时只针对DDF房源)
    # city: Newmarket (Newmarket Industrial Park) cmty: Newmarket Industrial Park
    if cmty and (src is 'DDF')
      specialChar = '[\\[\\]()+=&|><!{}^\"~*?:]'
      reg = new RegExp("#{specialChar}\s?#{cmty.toUpperCase()}\s?#{specialChar}",'i')
      result = result.replace(reg,'').trim()

  result = helpers_string.strCapitalize result,true
  subCities = staticCityFields.subCityNameList
  if subCities[prov]
    for city, val of subCities[prov]
      subCityRegex = new RegExp("^#{val.join('|')}$", 'i')
      if subCityRegex.test result
        prop.subCity = result # 原city改为subCity
        result = city
        break
  return result

###
# @description 对房源的city,prov,address进行格式化处理
# @params {object} {
  cnty: string  - country
  prov: string  - province  required房源基础属性
  city: string  - city  required房源基础属性
  cmty: string  - community
  zip: string  - postal code
  addr: string  - address 如果不存在,由st_num,st,st_sfx组成
  st: string  - street
  st_num: string  - street number
  st_sfx: string  - street type
  st_dir: string  - street direction N/S/W/E
  ptype2: array  - required 用来判断是否需要从address中分离unit信息
  saletp: array  - required 用来判断是否需要从address中分离unit信息
  mfee: string  - not required  用来判断是否需要从address中分离unit信息
  unt: string  - not required 用来判断是否需要从address中分离unit信息
}
# NOTE: 该函数可能被调用多次
###
module.exports.formatProvAndCity = formatProvAndCity = (obj,returnMatchedCases=false)->
  #format obj.cnty,U.S.A->USA, America -> USA,
  return unless obj
  #检查数字字段，error，但是继续处理
  for fld in ['city','cnty','prov','addr']
    if (typeof obj[fld] is 'number') or helpers_number.isNumber obj[fld]
      debug.warn fld,' is number',obj
      obj[fld] = ''+obj[fld]
    else
      obj[fld] = obj[fld].trim() if obj[fld]

  # 新的reso数据中st_dir和st_sfx在mapping过程中可能存在
  if obj.st_dir
    obj.st_dir = DIRECTION_ABBR[obj.st_dir.toString().toUpperCase()] or obj.st_dir.toUpperCase()
  if obj.st_sfx
    obj.st_sfx = helpers_string.strCapitalize(STREET_ABBR_CA_EN[obj.st_sfx.toString().toUpperCase()]) or obj.st_sfx
  obj.st = streetCorrectHelper.correctSt obj.prov, obj.city, obj.st
  # NOTE: addr可能为空字符串, 优先使用addr，然后从origAddr提取，最后使用buildAddrFromSt
  if (not obj.addr) and obj.origAddr
    obj.addr = extractAddressFromOrigAddr(obj.origAddr)
  obj.addr = obj.addr or (buildAddrFromSt obj)
  # NOTE: addr可能被objectStr2NumNClean转成number,需要转回string
  obj.addr += '' if obj.addr?
  obj.addr = obj.addr?.replace /\|/g, ' '
  # 处理多个连续空格
  obj.addr = obj.addr?.replace /\s+/g, ' '
  # Cannot read properties of undefined (reading 'trim')
  obj.addr = obj.addr?.trim()
  obj.zip = filterZip obj.zip if obj.zip

  if obj.cnty
    obj.cnty = cityHelper.getCntyAbbrName obj.cnty
  if obj.prov
    obj.prov = cityHelper.getProvAbbrName(obj.prov)

  if obj.city
    obj.city = checkPropCity obj
  
  if typeof obj.cmty is 'string'
    obj.cmty = correctCmty obj.cmty
  else if typeof obj.cmty is 'number'
    debug.error 'cmty is not string',obj

  if obj.addr and (checkIsRunFuncSeparateUnitFromAddr obj)
    obj.origUnt = obj.unt.toString()  if obj.unt# 保存原始unit
    # addr与unt都传入，先根据unt去匹配
    addrObj = separateUnitFromAddr(obj.addr,obj.unt)
    if addrObj.unt
      obj.unt = addrObj.unt # use split unt.
    obj.addr = addrObj.addr
    # daddr?
    obj.showAddr ?= addrObj.showAddr
    if ('u' in addrObj.cases)
      obj.partialRent = 'basement'
    if returnMatchedCases
      obj.cases = addrObj.cases
    # origSt = obj.st
    #if not obj.st
    #format street and st_num, get from addr
  else
    # 不需要调用separateUnitFromAddr的情况，直接将addr赋值为showAddr
    obj.showAddr ?= obj.addr.trim() if obj.addr

  if obj.addr
    # NOTE:不用分离unit的addr需要format
    obj.addr = formatAddr obj.addr
    addrArr = parseSfxAndDir obj
    if addrArr?.length > 1
      if STREET_NUMBER_REGEX.test addrArr[0]
        # 删除st_num前后的-
        if (addrArr[0].startsWith('-')) or (addrArr[0].endsWith('-'))
          addrArr[0] = addrArr[0].replace('-','')
        obj.st_num = addrArr[0]
        addrArr.shift()
        obj.st = addrArr.join(' ')
        
      else if STREET_NUMBER_REGEX.test addrArr[1] #Lot 20 abc street
        # 删除st_num前后的-
        if (addrArr[1].startsWith('-')) or (addrArr[1].endsWith('-'))
          addrArr[1] = addrArr[1].replace('-','')
        if (addrArr[0].toUpperCase() in ['LOT','LT'])
          obj.st_num =addrArr[0]+ ' ' +addrArr[1]
          addrArr = addrArr.splice(2)
        else
          obj.st_num = addrArr[1]
          obj.sp_st_num = true  # flag for special street number
          addrArr.splice(1,1)
        obj.st ?= addrArr.join(' ')

  if obj.unt?
    obj.unt = (obj.unt+'').trim()
  if obj.st #in some case, it is number
    obj.st = ''+obj.st
  obj.faddr ?= getFullAddress obj
  # debug.info 'formatProvAndCity','obj.unt:',obj.unt,' obj.st_num:',obj.st_num,' addr:',obj.addr
  return obj


###
@description 从uaddr中解析出countrty,province,city,zip,addr
@param {string} uaddr
@return  {object} {
  cnty: string  - country
  prov: string  - province
  city: string  - city
  zip3: string  - zip前三位,可以为null
  addr: string  - address
}
###
exports.decodeFromUaddr = decodeFromUaddr = (uaddr)->
  unless uaddr
    return
  arr = uaddr.split(':')
  if arr.length < 4
    debug.error 'uaddr is not composed of four parts :',uaddr
    return
  country = arr.shift()
  province = arr.shift()
  city = arr.shift()
  # 邮编前三位构造 字母数字字母 eg: A1B
  if (/^[A-Z][0-9][A-Z]$/.test arr[0]) and (arr.length > 1)
    zip3 = arr.shift()
    addr = arr.join(':')
  else
    addr = arr.join(':')

  ret =
    addr: helpers_string.strCapitalize addr
    city: helpers_string.strCapitalize city
    prov: helpers_string.strCapitalize province
    cnty: cityHelper.getCntyAbbrName country
  if zip3
    ret.zip3 = zip3
  ret

###
@description 根据DIRECTION_ABBR转换房屋朝向缩写
@param {string|Array<string>} fce - 房屋朝向，可以是字符串或数组
@return {Array<string>|undefined} 标准化后的朝向缩写数组，无效输入返回undefined
###
exports.abbrExposure = abbrExposure = (fce) ->
  # 1. fce如果不存在,直接return
  return unless fce
  
  result = []
  
  # 2. fce如果为数组，循环数组与DIRECTION_ABBR进行mapping
  if Array.isArray(fce)
    for direction in fce
      continue unless direction
      direction = String(direction).toUpperCase().trim()
      # 如果没有找到保留原数据
      mappedDirection = DIRECTION_ABBR[direction] or direction
      result.push(mappedDirection)
    return result
  
  # 3. fce如果为字符串，对fce按','分隔，每个部分与DIRECTION_ABBR进行mapping
  if 'string' is typeof fce
    # 转换为大写并去除空格
    fce = fce.toUpperCase().replace(/\s+/g, '')
    
    # 按逗号分隔，并处理每一部分
    directions = fce.split(',')
    
    for direction in directions
      # 去除每部分可能存在的空格
      direction = direction.trim()
      # 跳过空值
      continue unless direction
      # 从DIRECTION_ABBR中获取标准化缩写或保留原值
      mappedDirection = DIRECTION_ABBR[direction] or direction
      result.push(mappedDirection)
    
    return result
  
  # 4. fce不是数组与字符串时，需要warning log并return
  debug.warn 'abbrExposure: fce类型不支持:', typeof fce, fce
  return
